rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // 默认规则：拒绝所有读写操作
    // 这是一个重要的安全实践，可以防止任何未经明确授权的访问。
    match /{document=**} {
      allow read, write: if false;
    }

    // "prompts" 集合的特定规则
    match /prompts/{promptId} {

      // 读取规则
      // GET: 用户只能获取其 userId 与自身 uid 匹配的单个文档。
      allow get: if request.auth != null && resource.data.userId == request.auth.uid;
      // LIST: 登录用户可以查询列表。安全性由客户端查询中的 .where("userId", "==", uid) 子句强制执行。
      allow list: if request.auth != null;

      // 创建规则 (create)
      // 用户必须登录才能创建文档 (`request.auth != null`)。
      // 新文档的 `userId` 字段必须设置为创建者自己的 `uid`。
      // 这可以防止用户冒充他人创建数据。
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;

      // 更新/删除规则 (update, delete)
      // 用户只能更新或删除自己的文档。
      // 在执行操作前，规则会验证现有文档 (`resource`) 的 `userId` 是否与当前用户匹配。
      allow update, delete: if request.auth != null && resource.data.userId == request.auth.uid;
    }
  }
}