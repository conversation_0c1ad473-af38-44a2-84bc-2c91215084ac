// --- Firebase Configuration ---
const firebaseConfig = {
  apiKey: "AIzaSyD6tfWP9UsAii3KpnGh7j8zi0xKDU3PI8k",
  authDomain: "ai-pro-dc85a.firebaseapp.com",
  projectId: "ai-pro-dc85a",
  storageBucket: "ai-pro-dc85a.appspot.com",
  messagingSenderId: "178209437632",
  appId: "1:178209437632:web:b442106195a075a9fb7a25",
  measurementId: "G-26PG0VPL2Y"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore(); // Initialize Firestore

// --- DOM Elements ---
const authContainer = document.getElementById('auth-container');
const appContainer = document.getElementById('app-container');
const userInfo = document.getElementById('user-info');
const userEmailSpan = document.getElementById('user-email');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const loginBtn = document.getElementById('login-btn');
const registerBtn = document.getElementById('register-btn');
const logoutBtn = document.getElementById('logout-btn');
const viewPromptsBtn = document.getElementById('view-prompts-btn');
const addPromptBtn = document.getElementById('add-prompt-btn');
const viewPromptsView = document.getElementById('view-prompts-view');
const addPromptView = document.getElementById('add-prompt-view');
const views = document.querySelectorAll('.view');
const navBtns = document.querySelectorAll('.nav-btn');
const addPromptForm = document.getElementById('add-prompt-form');
const promptList = document.getElementById('prompt-list');
const promptTitleInput = document.getElementById('prompt-title');
const promptTextInput = document.getElementById('prompt-text');
const promptTagsInput = document.getElementById('prompt-tags');
const submitButton = addPromptForm.querySelector('button[type="submit"]');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const loadingIndicator = document.getElementById('loading-indicator'); // A general loading indicator
const promptSearch = document.getElementById('prompt-search');
const tagFilter = document.getElementById('tag-filter');
const exportDataBtn = document.getElementById('export-data-btn');
const importDataBtn = document.getElementById('import-data-btn');
const importFileInput = document.getElementById('import-file-input');
const popupOverlay = document.getElementById('popup-overlay');
const promptPopup = document.getElementById('prompt-popup');
const closePopupBtn = document.getElementById('close-popup-btn');
const copyPopupBtn = document.getElementById('copy-popup-btn');
const popupTitle = document.getElementById('popup-title');
const popupContent = document.getElementById('popup-content');
const popupTags = document.getElementById('popup-tags');

// --- State ---
let currentUser = null;
let editingPromptId = null;
let allPrompts = []; // 存储所有提示词数据用于搜索过滤
let filteredPrompts = []; // 存储过滤后的提示词数据
let currentPage = 1;
let itemsPerPage = 6; // 每页显示的提示词数量

// --- Loading State Handler ---
function showLoading(message) {
    if (loadingIndicator) {
        loadingIndicator.textContent = message;
        loadingIndicator.style.display = 'block';
    }
    // Disable all major buttons
    loginBtn.disabled = true;
    registerBtn.disabled = true;
    logoutBtn.disabled = true;
    submitButton.disabled = true;
}

function hideLoading() {
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
    // Enable all major buttons
    loginBtn.disabled = false;
    registerBtn.disabled = false;
    logoutBtn.disabled = false;
    submitButton.disabled = false;
    resetSubmitButtonState(); // Also reset the submit button text
}


// --- Auth State Change Handler ---
auth.onAuthStateChanged(async user => {
    if (user) {
        currentUser = user;
        console.log("User is signed in:", user.email);
        authContainer.style.display = 'none';
        appContainer.style.display = 'block';
        userInfo.style.display = 'block';
        userEmailSpan.textContent = user.email;
        setActiveView(viewPromptsView, viewPromptsBtn);
        await loadPrompts(user.uid);
    } else {
        currentUser = null;
        console.log("User is signed out.");
        authContainer.style.display = 'block';
        appContainer.style.display = 'none';
        userInfo.style.display = 'none';
        userEmailSpan.textContent = '';
        promptList.innerHTML = ''; // Clear prompts list on logout
    }
});

// --- Auth Event Listeners ---
registerBtn.addEventListener('click', async () => {
    const email = emailInput.value;
    const password = passwordInput.value;
    if (!email || !password) {
        alert("邮箱和密码不能为空！");
        return;
    }
    showLoading('注册中...');
    try {
        await auth.createUserWithEmailAndPassword(email, password);
        console.log("User registered successfully");
    } catch (error) {
        console.error("Error registering user:", error);
        alert(`注册失败: ${error.message}`);
    } finally {
        hideLoading();
    }
});

loginBtn.addEventListener('click', async () => {
    const email = emailInput.value;
    const password = passwordInput.value;
    if (!email || !password) {
        alert("邮箱和密码不能为空！");
        return;
    }
    showLoading('登录中...');
    try {
        await auth.signInWithEmailAndPassword(email, password);
        console.log("User logged in successfully");
    } catch (error) {
        console.error("Error logging in:", error);
        alert(`登录失败: ${error.message}`);
    } finally {
        hideLoading();
    }
});

logoutBtn.addEventListener('click', async () => {
    showLoading('登出中...');
    try {
        await auth.signOut();
        console.log("User logged out successfully");
    } catch (error) {
        console.error("Error logging out:", error);
        alert(`登出失败: ${error.message}`);
    } finally {
        hideLoading();
    }
});


// --- Firestore Data Operations ---

/**
 * Loads prompts from Firestore for the given user ID.
 * @param {string} uid The user's unique ID.
 */
async function loadPrompts(uid) {
    showLoading('正在加载提示词...');
    promptList.innerHTML = ''; // Clear previous list

    try {
        const snapshot = await db.collection('prompts')
            .where("userId", "==", uid)
            .orderBy("createdAt", "desc")
            .get();

        if (snapshot.empty) {
            promptList.innerHTML = '<li style="list-style: none; text-align: center; padding: 2rem; color: var(--text-light);">还没有任何提示词，快去添加一个吧！</li>';
            return;
        }

        allPrompts = []; // 清空之前的数据
        snapshot.forEach(doc => {
            const prompt = doc.data();
            const promptData = {
                id: doc.id,
                title: prompt.title,
                text: prompt.text,
                tags: prompt.tags || []
            };
            allPrompts.push(promptData); // 存储到全局数组用于搜索过滤
        });

        // 更新标签过滤器选项
        updateTagFilter();

        // 初始化显示（使用分页）
        filteredPrompts = [...allPrompts];
        currentPage = 1;
        displayCurrentPage();
        updatePaginationControls();
    } catch (error) {
        console.error("Error loading prompts from Firestore:", error);
        alert(`加载提示词失败: ${error.message}`);
        promptList.innerHTML = '<li style="list-style: none; text-align: center; padding: 2rem; color: var(--danger-color);">加载数据时出错，请稍后重试。</li>';
    } finally {
        hideLoading();
    }
}

/**
 * 创建提示词元素
 */
function createPromptElement(promptData) {
    const promptElement = document.createElement('li');
    promptElement.className = 'prompt-item';
    promptElement.setAttribute('data-id', promptData.id);
    promptElement.setAttribute('data-title', promptData.title);
    promptElement.setAttribute('data-text', promptData.text);
    promptElement.setAttribute('data-tags', promptData.tags.join(','));

    // 检查文本长度，决定是否显示展开按钮
    const isLongText = promptData.text.length > 150;

    promptElement.innerHTML = `
        <h3>${promptData.title}</h3>
        <div class="prompt-content">
            <p class="prompt-text">${promptData.text}</p>
            ${isLongText ? '<button class="toggle-btn" style="display: block;">查看详情</button>' : ''}
        </div>
        <div class="tags">
            ${promptData.tags.map(tag => `<span>${tag}</span>`).join('')}
        </div>
        <div class="prompt-actions">
            <button class="copy-btn">复制</button>
            <button class="edit-btn">编辑</button>
            <button class="delete-btn">删除</button>
        </div>
    `;

    return promptElement;
}

/**
 * 更新标签过滤器选项
 */
function updateTagFilter() {
    const allTags = new Set();
    allPrompts.forEach(prompt => {
        prompt.tags.forEach(tag => allTags.add(tag));
    });

    // 清空现有选项（保留"所有标签"选项）
    tagFilter.innerHTML = '<option value="">所有标签</option>';

    // 添加标签选项
    Array.from(allTags).sort().forEach(tag => {
        const option = document.createElement('option');
        option.value = tag;
        option.textContent = tag;
        tagFilter.appendChild(option);
    });
}

/**
 * 过滤和显示提示词
 */
function filterAndDisplayPrompts() {
    const searchTerm = promptSearch.value.toLowerCase().trim();
    const selectedTag = tagFilter.value;

    filteredPrompts = allPrompts.filter(prompt => {
        const matchesSearch = !searchTerm ||
            prompt.title.toLowerCase().includes(searchTerm) ||
            prompt.text.toLowerCase().includes(searchTerm);

        const matchesTag = !selectedTag || prompt.tags.includes(selectedTag);

        return matchesSearch && matchesTag;
    });

    // 重置到第一页
    currentPage = 1;

    // 显示当前页的提示词
    displayCurrentPage();

    // 更新分页控件
    updatePaginationControls();
}

/**
 * 显示当前页的提示词
 */
function displayCurrentPage() {
    // 清空列表
    promptList.innerHTML = '';

    if (filteredPrompts.length === 0) {
        promptList.innerHTML = '<li style="list-style: none; text-align: center; padding: 2rem; color: var(--text-light);">没有找到匹配的提示词</li>';
        return;
    }

    // 计算当前页的数据范围
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentPagePrompts = filteredPrompts.slice(startIndex, endIndex);

    // 显示当前页的提示词
    currentPagePrompts.forEach(promptData => {
        const promptElement = createPromptElement(promptData);
        promptList.appendChild(promptElement);
    });
}

/**
 * 更新分页控件
 */
function updatePaginationControls() {
    const paginationControls = document.getElementById('pagination-controls');

    if (filteredPrompts.length <= itemsPerPage) {
        paginationControls.innerHTML = '';
        return;
    }

    const totalPages = Math.ceil(filteredPrompts.length / itemsPerPage);

    paginationControls.innerHTML = `
        <button class="pagination-btn" id="prev-page" ${currentPage === 1 ? 'disabled' : ''}>
            上一页
        </button>
        <span class="page-info">
            第 ${currentPage} 页，共 ${totalPages} 页 (${filteredPrompts.length} 条记录)
        </span>
        <button class="pagination-btn" id="next-page" ${currentPage === totalPages ? 'disabled' : ''}>
            下一页
        </button>
    `;

    // 添加分页按钮事件监听器
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                displayCurrentPage();
                updatePaginationControls();
            }
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            if (currentPage < totalPages) {
                currentPage++;
                displayCurrentPage();
                updatePaginationControls();
            }
        });
    }
}

// --- Form Handling (Add/Update to Firestore) ---
addPromptForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    if (!currentUser) {
        alert('请先登录后再操作！');
        return;
    }

    const title = promptTitleInput.value.trim();
    const text = promptTextInput.value.trim();
    const tags = promptTagsInput.value.trim().split(',').map(tag => tag.trim()).filter(tag => tag !== '');

    if (!title || !text) {
        alert('标题和提示词内容不能为空！');
        return;
    }

    const loadingMessage = editingPromptId ? '更新中...' : '添加中...';
    showLoading(loadingMessage);
    submitButton.textContent = loadingMessage;


    try {
        if (editingPromptId) {
            // Update existing prompt
            await db.collection('prompts').doc(editingPromptId).update({
                title,
                text,
                tags
            });
            console.log("Prompt updated successfully in Firestore:", editingPromptId);
        } else {
            // Add new prompt
            await db.collection('prompts').add({
                title,
                text,
                tags,
                userId: currentUser.uid,
                createdAt: firebase.firestore.FieldValue.serverTimestamp()
            });
            console.log("Prompt added successfully to Firestore");
        }
        resetAddFormState();
        setActiveView(viewPromptsView, viewPromptsBtn);
        await loadPrompts(currentUser.uid); // Reload list to reflect changes
    } catch (error) {
        console.error("Error saving prompt to Firestore:", error);
        alert(`保存提示词失败: ${error.message}`);
    } finally {
        hideLoading();
    }
});

// --- Prompt List Event Delegation (Edit/Delete/Copy/Toggle) ---
promptList.addEventListener('click', async (event) => {
    const target = event.target;
    const promptItem = target.closest('.prompt-item');
    if (!promptItem) return;

    const promptId = promptItem.dataset.id;

    if (target.classList.contains('copy-btn')) {
        // Copy operation
        const promptText = promptItem.dataset.text;
        try {
            await navigator.clipboard.writeText(promptText);
            target.textContent = '已复制';
            target.disabled = true;
            setTimeout(() => {
                target.textContent = '复制';
                target.disabled = false;
            }, 2000);
        } catch (error) {
            console.error('复制失败:', error);
            alert('复制失败，请手动复制');
        }
    } else if (target.classList.contains('toggle-btn')) {
        // 显示详情弹窗
        const promptData = {
            id: promptId,
            title: promptItem.dataset.title,
            text: promptItem.dataset.text,
            tags: promptItem.dataset.tags ? promptItem.dataset.tags.split(',') : []
        };
        showPromptPopup(promptData);
    } else if (target.classList.contains('delete-btn')) {
        // Delete operation
        if (confirm('确定要删除这个提示词吗？')) {
            showLoading('删除中...');
            try {
                await db.collection('prompts').doc(promptId).delete();
                console.log('Prompt deleted:', promptId);
                promptItem.remove(); // Optimistically remove from UI
            } catch (error) {
                console.error('Error deleting prompt:', error);
                alert(`删除失败: ${error.message}`);
            } finally {
                hideLoading();
            }
        }
    } else if (target.classList.contains('edit-btn')) {
        // Edit operation
        try {
            showLoading('正在加载编辑数据...');
            const doc = await db.collection('prompts').doc(promptId).get();
            if (!doc.exists) {
                alert('无法找到要编辑的提示词。');
                return;
            }
            const prompt = doc.data();
            promptTitleInput.value = prompt.title;
            promptTextInput.value = prompt.text;
            promptTagsInput.value = prompt.tags.join(', ');
            editingPromptId = doc.id;

            // Switch to add/edit view
            addPromptView.querySelector('h2').textContent = '编辑提示词';
            submitButton.textContent = '更新提示词';
            cancelEditBtn.style.display = 'inline-block';
            setActiveView(addPromptView, addPromptBtn);
        } catch (error) {
            console.error('Error fetching prompt for edit:', error);
            alert(`加载编辑数据失败: ${error.message}`);
        } finally {
            hideLoading();
        }
    }
});

// --- Data Import/Export Functions ---

/**
 * 导出用户的所有提示词数据为JSON文件
 */
async function exportData() {
    if (!currentUser) {
        alert('请先登录后再操作！');
        return;
    }

    showLoading('正在导出数据...');

    try {
        const snapshot = await db.collection('prompts')
            .where("userId", "==", currentUser.uid)
            .orderBy("createdAt", "desc")
            .get();

        const exportData = {
            exportDate: new Date().toISOString(),
            userEmail: currentUser.email,
            prompts: []
        };

        snapshot.forEach(doc => {
            const prompt = doc.data();
            exportData.prompts.push({
                id: doc.id,
                title: prompt.title,
                text: prompt.text,
                tags: prompt.tags,
                createdAt: prompt.createdAt ? prompt.createdAt.toDate().toISOString() : new Date().toISOString()
            });
        });

        // 创建下载链接
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `ai-prompts-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        alert(`成功导出 ${exportData.prompts.length} 条提示词数据！`);
    } catch (error) {
        console.error("Error exporting data:", error);
        alert(`导出数据失败: ${error.message}`);
    } finally {
        hideLoading();
    }
}

/**
 * 导入JSON文件中的提示词数据
 */
async function importData(file) {
    if (!currentUser) {
        alert('请先登录后再操作！');
        return;
    }

    if (!file) {
        alert('请选择要导入的文件！');
        return;
    }

    showLoading('正在导入数据...');

    try {
        const fileContent = await readFileAsText(file);
        const importData = JSON.parse(fileContent);

        // 验证数据格式
        if (!importData.prompts || !Array.isArray(importData.prompts)) {
            throw new Error('无效的数据格式：缺少prompts数组');
        }

        let successCount = 0;
        let errorCount = 0;

        // 批量导入数据
        for (const promptData of importData.prompts) {
            try {
                // 验证必要字段
                if (!promptData.title || !promptData.text) {
                    console.warn('跳过无效数据:', promptData);
                    errorCount++;
                    continue;
                }

                await db.collection('prompts').add({
                    title: promptData.title,
                    text: promptData.text,
                    tags: promptData.tags || [],
                    userId: currentUser.uid,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp()
                });
                successCount++;
            } catch (error) {
                console.error('导入单条数据失败:', error);
                errorCount++;
            }
        }

        alert(`导入完成！成功: ${successCount} 条，失败: ${errorCount} 条`);

        // 重新加载提示词列表
        await loadPrompts(currentUser.uid);

    } catch (error) {
        console.error("Error importing data:", error);
        alert(`导入数据失败: ${error.message}`);
    } finally {
        hideLoading();
    }
}

/**
 * 读取文件内容为文本
 */
function readFileAsText(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(new Error('文件读取失败'));
        reader.readAsText(file);
    });
}

// --- Popup Functions ---

/**
 * 显示提示词详情弹窗
 */
function showPromptPopup(promptData) {
    popupTitle.textContent = promptData.title;
    popupContent.textContent = promptData.text;

    // 显示标签
    popupTags.innerHTML = '';
    if (promptData.tags && promptData.tags.length > 0) {
        promptData.tags.forEach(tag => {
            const tagSpan = document.createElement('span');
            tagSpan.textContent = tag;
            popupTags.appendChild(tagSpan);
        });
    }

    // 存储当前弹窗的提示词数据，用于复制功能
    copyPopupBtn.dataset.text = promptData.text;

    // 显示弹窗
    popupOverlay.style.display = 'block';
    promptPopup.style.display = 'block';

    // 添加动画效果
    setTimeout(() => {
        popupOverlay.style.opacity = '1';
        promptPopup.style.opacity = '1';
        promptPopup.style.transform = 'translate(-50%, -50%) scale(1)';
    }, 10);

    // 防止页面滚动
    document.body.style.overflow = 'hidden';
}

/**
 * 隐藏提示词详情弹窗
 */
function hidePromptPopup() {
    // 添加关闭动画
    popupOverlay.style.opacity = '0';
    promptPopup.style.opacity = '0';
    promptPopup.style.transform = 'translate(-50%, -50%) scale(0.9)';

    setTimeout(() => {
        popupOverlay.style.display = 'none';
        promptPopup.style.display = 'none';
        // 恢复页面滚动
        document.body.style.overflow = '';
    }, 300);
}

/**
 * 初始化弹窗样式
 */
function initPopupStyles() {
    popupOverlay.style.opacity = '0';
    popupOverlay.style.transition = 'opacity 0.3s ease-in-out';

    promptPopup.style.opacity = '0';
    promptPopup.style.transform = 'translate(-50%, -50%) scale(0.9)';
    promptPopup.style.transition = 'all 0.3s ease-in-out';
}

// 初始化弹窗样式
initPopupStyles();


// --- View Switching & UI Helpers ---
function setActiveView(viewToShow, btnToActivate) {
    views.forEach(view => view.classList.remove('active'));
    navBtns.forEach(btn => btn.classList.remove('active'));
    if (viewToShow) viewToShow.classList.add('active');
    if (btnToActivate) btnToActivate.classList.add('active');
}

viewPromptsBtn.addEventListener('click', () => {
    setActiveView(viewPromptsView, viewPromptsBtn);
});

addPromptBtn.addEventListener('click', () => {
    resetAddFormState();
    setActiveView(addPromptView, addPromptBtn);
});

cancelEditBtn.addEventListener('click', () => {
    resetAddFormState();
    setActiveView(viewPromptsView, viewPromptsBtn);
});

// --- Data Import/Export Event Listeners ---
exportDataBtn.addEventListener('click', exportData);

importDataBtn.addEventListener('click', () => {
    importFileInput.click();
});

importFileInput.addEventListener('change', (event) => {
    const file = event.target.files[0];
    if (file) {
        if (confirm(`确定要导入文件 "${file.name}" 吗？这将添加新的提示词到您的收藏中。`)) {
            importData(file);
        }
        // 清空文件输入，允许重复选择同一文件
        event.target.value = '';
    }
});

// --- Search and Filter Event Listeners ---
promptSearch.addEventListener('input', filterAndDisplayPrompts);
tagFilter.addEventListener('change', filterAndDisplayPrompts);

// --- Popup Event Listeners ---
closePopupBtn.addEventListener('click', hidePromptPopup);

popupOverlay.addEventListener('click', hidePromptPopup);

copyPopupBtn.addEventListener('click', async () => {
    const textToCopy = copyPopupBtn.dataset.text;
    if (!textToCopy) return;

    try {
        await navigator.clipboard.writeText(textToCopy);
        copyPopupBtn.textContent = '已复制';
        copyPopupBtn.disabled = true;
        setTimeout(() => {
            copyPopupBtn.textContent = '复制内容';
            copyPopupBtn.disabled = false;
        }, 2000);
    } catch (error) {
        console.error('复制失败:', error);
        alert('复制失败，请手动复制');
    }
});

// 键盘快捷键支持
document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape' && promptPopup.style.display === 'block') {
        hidePromptPopup();
    }
});

function resetAddFormState() {
    addPromptForm.reset();
    editingPromptId = null;
    addPromptView.querySelector('h2').textContent = '添加新提示词';
    cancelEditBtn.style.display = 'none';
    resetSubmitButtonState();
}

function resetSubmitButtonState() {
    if (submitButton) {
        submitButton.disabled = false;
        submitButton.textContent = editingPromptId ? '更新提示词' : '添加收藏';
    }
}
