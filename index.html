<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 提示词收藏夹</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <h1>我的 AI 提示词收藏</h1>
        <div id="user-info" style="display: none;">
            <span id="user-email"></span>
            <button id="logout-btn">登出</button>
        </div>
        <nav>
            <button id="view-prompts-btn" class="nav-btn active">查看收藏</button>
            <button id="add-prompt-btn" class="nav-btn">添加新提示词</button>
        </nav>
    </header>

    <div id="loading-indicator" class="loading-indicator" style="display: none;">正在加载...</div>

    <div id="auth-container">
        <h2>登录或注册</h2>
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" name="email" required>
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" name="password" required>
        </div>
        <div class="form-buttons">
            <button id="login-btn">登录</button>
            <button id="register-btn">注册</button>
        </div>
    </div>

    <div id="app-container" style="display: none;">
        <main>
            <!-- 查看提示词视图 -->
            <div id="view-prompts-view" class="view active">
                <section id="prompt-list-section">
                    <div class="section-header">
                        <h2>已收藏的提示词</h2>
                        <div class="data-management">
                            <button id="export-data-btn" class="data-btn">导出数据</button>
                            <button id="import-data-btn" class="data-btn">导入数据</button>
                            <input type="file" id="import-file-input" accept=".json" style="display: none;">
                        </div>
                    </div>
                    
                    <!-- 添加搜索过滤器 -->
                    <div class="search-filter">
                        <input type="text" id="prompt-search" placeholder="搜索提示词..." aria-label="搜索提示词">
                        <select id="tag-filter" aria-label="按标签筛选">
                            <option value="">所有标签</option>
                        </select>
                    </div>
                    
                    <ul id="prompt-list">
                        <!-- 提示词将在这里动态添加 -->
                    </ul>
                    <!-- 分页控件容器 -->
                    <div id="pagination-controls" class="pagination-controls"></div>
                </section>
            </div> <!-- 结束查看视图 -->

            <!-- 添加提示词视图 -->
            <div id="add-prompt-view" class="view">
                <section id="add-prompt-section">
                    <h2>添加新提示词</h2>
                    <form id="add-prompt-form">
                        <div class="form-group">
                            <label for="prompt-title">标题:</label>
                            <input type="text" id="prompt-title" name="prompt-title" required>
                        </div>
                        <div class="form-group">
                            <label for="prompt-text">提示词内容:</label>
                            <textarea id="prompt-text" name="prompt-text" rows="6" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="prompt-tags">标签 (用逗号分隔):</label>
                            <input type="text" id="prompt-tags" name="prompt-tags">
                        </div>
                        <div class="form-buttons">
                            <button type="submit">添加收藏</button>
                            <button type="button" id="cancel-edit-btn" style="display: none;">取消</button>
                        </div>
                    </form>
                </section>
            </div> <!-- 结束添加视图 -->
        </main>
    </div>

    <footer>
        <p>&copy; 2025 AI 提示词收藏夹</p>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
    
    <!-- Your custom script -->
    <script src="script.js"></script>
</body>
</html>