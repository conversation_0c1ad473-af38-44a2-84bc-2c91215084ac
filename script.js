// --- Firebase Configuration ---
const firebaseConfig = {
  apiKey: "AIzaSyD6tfWP9UsAii3KpnGh7j8zi0xKDU3PI8k",
  authDomain: "ai-pro-dc85a.firebaseapp.com",
  projectId: "ai-pro-dc85a",
  storageBucket: "ai-pro-dc85a.appspot.com",
  messagingSenderId: "178209437632",
  appId: "1:178209437632:web:b442106195a075a9fb7a25",
  measurementId: "G-26PG0VPL2Y"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const auth = firebase.auth();
const db = firebase.firestore(); // Initialize Firestore

// --- DOM Elements ---
const authContainer = document.getElementById('auth-container');
const appContainer = document.getElementById('app-container');
const userInfo = document.getElementById('user-info');
const userEmailSpan = document.getElementById('user-email');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const loginBtn = document.getElementById('login-btn');
const registerBtn = document.getElementById('register-btn');
const logoutBtn = document.getElementById('logout-btn');
const viewPromptsBtn = document.getElementById('view-prompts-btn');
const addPromptBtn = document.getElementById('add-prompt-btn');
const viewPromptsView = document.getElementById('view-prompts-view');
const addPromptView = document.getElementById('add-prompt-view');
const views = document.querySelectorAll('.view');
const navBtns = document.querySelectorAll('.nav-btn');
const addPromptForm = document.getElementById('add-prompt-form');
const promptList = document.getElementById('prompt-list');
const promptTitleInput = document.getElementById('prompt-title');
const promptTextInput = document.getElementById('prompt-text');
const promptTagsInput = document.getElementById('prompt-tags');
const submitButton = addPromptForm.querySelector('button[type="submit"]');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const loadingIndicator = document.getElementById('loading-indicator'); // A general loading indicator
const promptSearch = document.getElementById('prompt-search');
const tagFilter = document.getElementById('tag-filter');

// --- State ---
let currentUser = null;
let editingPromptId = null;
let allPrompts = []; // 存储所有提示词数据用于搜索过滤

// --- Loading State Handler ---
function showLoading(message) {
    if (loadingIndicator) {
        loadingIndicator.textContent = message;
        loadingIndicator.style.display = 'block';
    }
    // Disable all major buttons
    loginBtn.disabled = true;
    registerBtn.disabled = true;
    logoutBtn.disabled = true;
    submitButton.disabled = true;
}

function hideLoading() {
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
    // Enable all major buttons
    loginBtn.disabled = false;
    registerBtn.disabled = false;
    logoutBtn.disabled = false;
    submitButton.disabled = false;
    resetSubmitButtonState(); // Also reset the submit button text
}


// --- Auth State Change Handler ---
auth.onAuthStateChanged(async user => {
    if (user) {
        currentUser = user;
        console.log("User is signed in:", user.email);
        authContainer.style.display = 'none';
        appContainer.style.display = 'block';
        userInfo.style.display = 'block';
        userEmailSpan.textContent = user.email;
        setActiveView(viewPromptsView, viewPromptsBtn);
        await loadPrompts(user.uid);
    } else {
        currentUser = null;
        console.log("User is signed out.");
        authContainer.style.display = 'block';
        appContainer.style.display = 'none';
        userInfo.style.display = 'none';
        userEmailSpan.textContent = '';
        promptList.innerHTML = ''; // Clear prompts list on logout
    }
});

// --- Auth Event Listeners ---
registerBtn.addEventListener('click', async () => {
    const email = emailInput.value;
    const password = passwordInput.value;
    if (!email || !password) {
        alert("邮箱和密码不能为空！");
        return;
    }
    showLoading('注册中...');
    try {
        await auth.createUserWithEmailAndPassword(email, password);
        console.log("User registered successfully");
    } catch (error) {
        console.error("Error registering user:", error);
        alert(`注册失败: ${error.message}`);
    } finally {
        hideLoading();
    }
});

loginBtn.addEventListener('click', async () => {
    const email = emailInput.value;
    const password = passwordInput.value;
    if (!email || !password) {
        alert("邮箱和密码不能为空！");
        return;
    }
    showLoading('登录中...');
    try {
        await auth.signInWithEmailAndPassword(email, password);
        console.log("User logged in successfully");
    } catch (error) {
        console.error("Error logging in:", error);
        alert(`登录失败: ${error.message}`);
    } finally {
        hideLoading();
    }
});

logoutBtn.addEventListener('click', async () => {
    showLoading('登出中...');
    try {
        await auth.signOut();
        console.log("User logged out successfully");
    } catch (error) {
        console.error("Error logging out:", error);
        alert(`登出失败: ${error.message}`);
    } finally {
        hideLoading();
    }
});


// --- Firestore Data Operations ---

/**
 * Loads prompts from Firestore for the given user ID.
 * @param {string} uid The user's unique ID.
 */
async function loadPrompts(uid) {
    showLoading('正在加载提示词...');
    promptList.innerHTML = ''; // Clear previous list

    try {
        const snapshot = await db.collection('prompts')
            .where("userId", "==", uid)
            .orderBy("createdAt", "desc")
            .get();

        if (snapshot.empty) {
            promptList.innerHTML = '<li style="list-style: none; text-align: center; padding: 2rem; color: var(--text-light);">还没有任何提示词，快去添加一个吧！</li>';
            return;
        }

        snapshot.forEach(doc => {
            const prompt = doc.data();
            const promptElement = document.createElement('li');
            promptElement.className = 'prompt-item';
            promptElement.setAttribute('data-id', doc.id);
            promptElement.setAttribute('data-title', prompt.title);
            promptElement.setAttribute('data-text', prompt.text);
            promptElement.setAttribute('data-tags', prompt.tags.join(','));

            // 检查文本长度，决定是否显示展开按钮
            const isLongText = prompt.text.length > 150;

            promptElement.innerHTML = `
                <h3>${prompt.title}</h3>
                <div class="prompt-content">
                    <p class="prompt-text">${prompt.text}</p>
                    ${isLongText ? '<button class="toggle-btn" style="display: block;">展开</button>' : ''}
                </div>
                <div class="tags">
                    ${prompt.tags.map(tag => `<span>${tag}</span>`).join('')}
                </div>
                <div class="prompt-actions">
                    <button class="copy-btn">复制</button>
                    <button class="edit-btn">编辑</button>
                    <button class="delete-btn">删除</button>
                </div>
            `;
            promptList.appendChild(promptElement);
        });
    } catch (error) {
        console.error("Error loading prompts from Firestore:", error);
        alert(`加载提示词失败: ${error.message}`);
        promptList.innerHTML = '<li style="list-style: none; text-align: center; padding: 2rem; color: var(--danger-color);">加载数据时出错，请稍后重试。</li>';
    } finally {
        hideLoading();
    }
}

// --- Form Handling (Add/Update to Firestore) ---
addPromptForm.addEventListener('submit', async (event) => {
    event.preventDefault();
    if (!currentUser) {
        alert('请先登录后再操作！');
        return;
    }

    const title = promptTitleInput.value.trim();
    const text = promptTextInput.value.trim();
    const tags = promptTagsInput.value.trim().split(',').map(tag => tag.trim()).filter(tag => tag !== '');

    if (!title || !text) {
        alert('标题和提示词内容不能为空！');
        return;
    }

    const loadingMessage = editingPromptId ? '更新中...' : '添加中...';
    showLoading(loadingMessage);
    submitButton.textContent = loadingMessage;


    try {
        if (editingPromptId) {
            // Update existing prompt
            await db.collection('prompts').doc(editingPromptId).update({
                title,
                text,
                tags
            });
            console.log("Prompt updated successfully in Firestore:", editingPromptId);
        } else {
            // Add new prompt
            await db.collection('prompts').add({
                title,
                text,
                tags,
                userId: currentUser.uid,
                createdAt: firebase.firestore.FieldValue.serverTimestamp()
            });
            console.log("Prompt added successfully to Firestore");
        }
        resetAddFormState();
        setActiveView(viewPromptsView, viewPromptsBtn);
        await loadPrompts(currentUser.uid); // Reload list to reflect changes
    } catch (error) {
        console.error("Error saving prompt to Firestore:", error);
        alert(`保存提示词失败: ${error.message}`);
    } finally {
        hideLoading();
    }
});

// --- Prompt List Event Delegation (Edit/Delete/Copy/Toggle) ---
promptList.addEventListener('click', async (event) => {
    const target = event.target;
    const promptItem = target.closest('.prompt-item');
    if (!promptItem) return;

    const promptId = promptItem.dataset.id;

    if (target.classList.contains('copy-btn')) {
        // Copy operation
        const promptText = promptItem.dataset.text;
        try {
            await navigator.clipboard.writeText(promptText);
            target.textContent = '已复制';
            target.disabled = true;
            setTimeout(() => {
                target.textContent = '复制';
                target.disabled = false;
            }, 2000);
        } catch (error) {
            console.error('复制失败:', error);
            alert('复制失败，请手动复制');
        }
    } else if (target.classList.contains('toggle-btn')) {
        // Toggle expand/collapse
        const promptText = promptItem.querySelector('.prompt-text');
        const isExpanded = promptText.classList.contains('expanded');

        if (isExpanded) {
            promptText.classList.remove('expanded');
            target.textContent = '展开';
        } else {
            promptText.classList.add('expanded');
            target.textContent = '折叠';
        }
    } else if (target.classList.contains('delete-btn')) {
        // Delete operation
        if (confirm('确定要删除这个提示词吗？')) {
            showLoading('删除中...');
            try {
                await db.collection('prompts').doc(promptId).delete();
                console.log('Prompt deleted:', promptId);
                promptItem.remove(); // Optimistically remove from UI
            } catch (error) {
                console.error('Error deleting prompt:', error);
                alert(`删除失败: ${error.message}`);
            } finally {
                hideLoading();
            }
        }
    } else if (target.classList.contains('edit-btn')) {
        // Edit operation
        try {
            showLoading('正在加载编辑数据...');
            const doc = await db.collection('prompts').doc(promptId).get();
            if (!doc.exists) {
                alert('无法找到要编辑的提示词。');
                return;
            }
            const prompt = doc.data();
            promptTitleInput.value = prompt.title;
            promptTextInput.value = prompt.text;
            promptTagsInput.value = prompt.tags.join(', ');
            editingPromptId = doc.id;

            // Switch to add/edit view
            addPromptView.querySelector('h2').textContent = '编辑提示词';
            submitButton.textContent = '更新提示词';
            cancelEditBtn.style.display = 'inline-block';
            setActiveView(addPromptView, addPromptBtn);
        } catch (error) {
            console.error('Error fetching prompt for edit:', error);
            alert(`加载编辑数据失败: ${error.message}`);
        } finally {
            hideLoading();
        }
    }
});


// --- View Switching & UI Helpers ---
function setActiveView(viewToShow, btnToActivate) {
    views.forEach(view => view.classList.remove('active'));
    navBtns.forEach(btn => btn.classList.remove('active'));
    if (viewToShow) viewToShow.classList.add('active');
    if (btnToActivate) btnToActivate.classList.add('active');
}

viewPromptsBtn.addEventListener('click', () => {
    setActiveView(viewPromptsView, viewPromptsBtn);
});

addPromptBtn.addEventListener('click', () => {
    resetAddFormState();
    setActiveView(addPromptView, addPromptBtn);
});

cancelEditBtn.addEventListener('click', () => {
    resetAddFormState();
    setActiveView(viewPromptsView, viewPromptsBtn);
});

function resetAddFormState() {
    addPromptForm.reset();
    editingPromptId = null;
    addPromptView.querySelector('h2').textContent = '添加新提示词';
    cancelEditBtn.style.display = 'none';
    resetSubmitButtonState();
}

function resetSubmitButtonState() {
    if (submitButton) {
        submitButton.disabled = false;
        submitButton.textContent = editingPromptId ? '更新提示词' : '添加收藏';
    }
}
